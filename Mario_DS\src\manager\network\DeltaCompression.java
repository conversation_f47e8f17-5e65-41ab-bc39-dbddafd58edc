package manager.network;

import java.util.concurrent.ConcurrentHashMap;

import manager.network.NetworkMessage.PlayerState;

/**
 * Implements delta compression for network messages to reduce bandwidth usage.
 * Only sends changed state data instead of full state updates.
 */
public class DeltaCompression {
    
    // Store last sent states for comparison
    private final ConcurrentHashMap<String, PlayerState> lastSentStates;
    
    // Thresholds for determining significant changes
    private static final double POSITION_THRESHOLD = 1.0; // 1 pixel
    private static final double VELOCITY_THRESHOLD = 0.5; // 0.5 units
    private static final int POINTS_THRESHOLD = 0; // Any point change is significant
    private static final int LIVES_THRESHOLD = 0; // Any life change is significant
    private static final int COINS_THRESHOLD = 0; // Any coin change is significant
    
    public DeltaCompression() {
        this.lastSentStates = new ConcurrentHashMap<>();
    }
    
    /**
     * Check if a player state has changed significantly enough to warrant sending
     */
    public boolean hasSignificantChange(String playerId, PlayerState currentState) {
        if (currentState == null) {
            return false;
        }
        
        PlayerState lastState = lastSentStates.get(playerId);
        if (lastState == null) {
            // First time sending this player's state
            return true;
        }
        
        return hasPositionChanged(lastState, currentState) ||
               hasVelocityChanged(lastState, currentState) ||
               hasGameStateChanged(lastState, currentState) ||
               hasPowerUpChanged(lastState, currentState) ||
               hasDimensionChanged(lastState, currentState);
    }
    
    /**
     * Record that a state has been sent
     */
    public void recordSentState(String playerId, PlayerState state) {
        if (state != null) {
            lastSentStates.put(playerId, state);
        }
    }
    
    /**
     * Create a delta state containing only changed fields
     */
    public PlayerStateDelta createDelta(String playerId, PlayerState currentState) {
        PlayerState lastState = lastSentStates.get(playerId);
        if (lastState == null) {
            // First time - send full state as delta
            return new PlayerStateDelta(currentState, true);
        }
        
        return new PlayerStateDelta(lastState, currentState);
    }
    
    /**
     * Check if position has changed significantly
     */
    private boolean hasPositionChanged(PlayerState last, PlayerState current) {
        double deltaX = Math.abs(current.getX() - last.getX());
        double deltaY = Math.abs(current.getY() - last.getY());
        return deltaX > POSITION_THRESHOLD || deltaY > POSITION_THRESHOLD;
    }
    
    /**
     * Check if velocity has changed significantly
     */
    private boolean hasVelocityChanged(PlayerState last, PlayerState current) {
        double deltaVelX = Math.abs(current.getVelX() - last.getVelX());
        double deltaVelY = Math.abs(current.getVelY() - last.getVelY());
        return deltaVelX > VELOCITY_THRESHOLD || deltaVelY > VELOCITY_THRESHOLD;
    }
    
    /**
     * Check if game state (points, lives, coins) has changed
     */
    private boolean hasGameStateChanged(PlayerState last, PlayerState current) {
        return Math.abs(current.getPoints() - last.getPoints()) > POINTS_THRESHOLD ||
               Math.abs(current.getLives() - last.getLives()) > LIVES_THRESHOLD ||
               Math.abs(current.getCoins() - last.getCoins()) > COINS_THRESHOLD ||
               current.isJumping() != last.isJumping() ||
               current.isFalling() != last.isFalling() ||
               current.isToRight() != last.isToRight();
    }
    
    /**
     * Check if power-up state has changed
     */
    private boolean hasPowerUpChanged(PlayerState last, PlayerState current) {
        return current.isSuper() != last.isSuper() ||
               current.isFire() != last.isFire();
    }
    
    /**
     * Check if dimensions have changed
     */
    private boolean hasDimensionChanged(PlayerState last, PlayerState current) {
        return current.getWidth() != last.getWidth() ||
               current.getHeight() != last.getHeight();
    }
    
    /**
     * Clear stored states (useful for reconnections)
     */
    public void clearStates() {
        lastSentStates.clear();
    }
    
    /**
     * Get compression ratio (percentage of data saved)
     */
    public double getCompressionRatio() {
        // This would require tracking sent vs. unsent updates
        // For now, return an estimate based on typical scenarios
        return 0.6; // Estimate 60% compression
    }
    
    /**
     * Represents a delta (difference) between two player states
     */
    public static class PlayerStateDelta {
        private final boolean isFullState;
        private final PlayerState fullState;
        
        // Changed fields
        private final boolean hasPositionChange;
        private final double deltaX, deltaY;
        
        private final boolean hasVelocityChange;
        private final double deltaVelX, deltaVelY;
        
        private final boolean hasGameStateChange;
        private final int deltaPoints, deltaLives, deltaCoins;
        private final boolean jumping, falling, toRight;
        
        private final boolean hasPowerUpChange;
        private final boolean isSuper, isFire;
        
        private final boolean hasDimensionChange;
        private final double width, height;
        
        public PlayerStateDelta(PlayerState fullState, boolean isFullState) {
            this.isFullState = isFullState;
            this.fullState = fullState;
            
            // For full state, mark all as changed
            this.hasPositionChange = true;
            this.deltaX = fullState.getX();
            this.deltaY = fullState.getY();
            
            this.hasVelocityChange = true;
            this.deltaVelX = fullState.getVelX();
            this.deltaVelY = fullState.getVelY();
            
            this.hasGameStateChange = true;
            this.deltaPoints = fullState.getPoints();
            this.deltaLives = fullState.getLives();
            this.deltaCoins = fullState.getCoins();
            this.jumping = fullState.isJumping();
            this.falling = fullState.isFalling();
            this.toRight = fullState.isToRight();
            
            this.hasPowerUpChange = true;
            this.isSuper = fullState.isSuper();
            this.isFire = fullState.isFire();
            
            this.hasDimensionChange = true;
            this.width = fullState.getWidth();
            this.height = fullState.getHeight();
        }
        
        public PlayerStateDelta(PlayerState lastState, PlayerState currentState) {
            this.isFullState = false;
            this.fullState = null;
            
            // Calculate position changes
            this.hasPositionChange = Math.abs(currentState.getX() - lastState.getX()) > POSITION_THRESHOLD ||
                                   Math.abs(currentState.getY() - lastState.getY()) > POSITION_THRESHOLD;
            this.deltaX = currentState.getX();
            this.deltaY = currentState.getY();
            
            // Calculate velocity changes
            this.hasVelocityChange = Math.abs(currentState.getVelX() - lastState.getVelX()) > VELOCITY_THRESHOLD ||
                                   Math.abs(currentState.getVelY() - lastState.getVelY()) > VELOCITY_THRESHOLD;
            this.deltaVelX = currentState.getVelX();
            this.deltaVelY = currentState.getVelY();
            
            // Calculate game state changes
            this.hasGameStateChange = currentState.getPoints() != lastState.getPoints() ||
                                    currentState.getLives() != lastState.getLives() ||
                                    currentState.getCoins() != lastState.getCoins() ||
                                    currentState.isJumping() != lastState.isJumping() ||
                                    currentState.isFalling() != lastState.isFalling() ||
                                    currentState.isToRight() != lastState.isToRight();
            this.deltaPoints = currentState.getPoints();
            this.deltaLives = currentState.getLives();
            this.deltaCoins = currentState.getCoins();
            this.jumping = currentState.isJumping();
            this.falling = currentState.isFalling();
            this.toRight = currentState.isToRight();
            
            // Calculate power-up changes
            this.hasPowerUpChange = currentState.isSuper() != lastState.isSuper() ||
                                  currentState.isFire() != lastState.isFire();
            this.isSuper = currentState.isSuper();
            this.isFire = currentState.isFire();
            
            // Calculate dimension changes
            this.hasDimensionChange = currentState.getWidth() != lastState.getWidth() ||
                                    currentState.getHeight() != lastState.getHeight();
            this.width = currentState.getWidth();
            this.height = currentState.getHeight();
        }
        
        // Getters
        public boolean isFullState() { return isFullState; }
        public PlayerState getFullState() { return fullState; }
        public boolean hasPositionChange() { return hasPositionChange; }
        public double getDeltaX() { return deltaX; }
        public double getDeltaY() { return deltaY; }
        public boolean hasVelocityChange() { return hasVelocityChange; }
        public double getDeltaVelX() { return deltaVelX; }
        public double getDeltaVelY() { return deltaVelY; }
        public boolean hasGameStateChange() { return hasGameStateChange; }
        public int getDeltaPoints() { return deltaPoints; }
        public int getDeltaLives() { return deltaLives; }
        public int getDeltaCoins() { return deltaCoins; }
        public boolean isJumping() { return jumping; }
        public boolean isFalling() { return falling; }
        public boolean isToRight() { return toRight; }
        public boolean hasPowerUpChange() { return hasPowerUpChange; }
        public boolean isSuper() { return isSuper; }
        public boolean isFire() { return isFire; }
        public boolean hasDimensionChange() { return hasDimensionChange; }
        public double getWidth() { return width; }
        public double getHeight() { return height; }
        
        /**
         * Check if this delta has any changes
         */
        public boolean hasAnyChanges() {
            return isFullState || hasPositionChange || hasVelocityChange || 
                   hasGameStateChange || hasPowerUpChange || hasDimensionChange;
        }
    }
}
