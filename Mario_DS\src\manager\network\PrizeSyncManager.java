package manager.network;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import manager.GameEngine;
import manager.network.NetworkMessage.PrizeCollectionMessage;
import model.hero.Mario;
import model.hero.MarioForm;
import model.prize.Prize;
import views.Animation;
import views.ImageLoader;

/**
 * Manages prize collection synchronization between server and client.
 * Ensures that prizes are only collected once and that the effects
 * (points, coins, power-ups) are properly synchronized.
 */
public class PrizeSyncManager {
    
    private final GameEngine gameEngine;
    private final boolean isServer;
    private final ConcurrentHashMap<String, Long> collectedPrizes;
    private final AtomicLong prizeIdCounter;
    
    // Prize collection tracking
    private static final long PRIZE_COLLECTION_TIMEOUT_MS = 5000; // 5 seconds
    
    public PrizeSyncManager(GameEngine gameEngine, boolean isServer) {
        this.gameEngine = gameEngine;
        this.isServer = isServer;
        this.collectedPrizes = new ConcurrentHashMap<>();
        this.prizeIdCounter = new AtomicLong(0);
    }
    
    /**
     * Handle prize collection on the authoritative side (server)
     */
    public PrizeCollectionMessage handlePrizeCollection(String playerId, Prize prize, double prizeX, double prizeY) {
        if (!isServer) {
            return null; // Only server should handle authoritative prize collection
        }
        
        // Generate unique prize ID based on position and type
        String prizeId = generatePrizeId(prize, prizeX, prizeY);
        
        // Check if this prize was already collected
        if (collectedPrizes.containsKey(prizeId)) {
            System.out.println("[PrizeSync] Prize already collected: " + prizeId);
            return null;
        }
        
        // Mark prize as collected
        collectedPrizes.put(prizeId, System.currentTimeMillis());
        
        // Get Mario object
        Mario mario = playerId.equals("mario") ? gameEngine.getMario() : gameEngine.getMario2();
        if (mario == null) {
            return null;
        }
        
        // Store original state
        boolean originalSuper = mario.getMarioForm().isSuper();
        boolean originalFire = mario.getMarioForm().isFire();
        int originalPoints = mario.getPoints();
        int originalCoins = mario.getCoins();
        
        // Apply prize effects
        String prizeType = prize.getClass().getSimpleName();
        applyPrizeEffects(mario, prize, prizeType);
        
        // Check if power-up state changed
        boolean powerUpChanged = (originalSuper != mario.getMarioForm().isSuper()) || 
                                (originalFire != mario.getMarioForm().isFire());
        
        // Create synchronization message
        PrizeCollectionMessage message = new PrizeCollectionMessage(
            playerId, prizeType, prize.getPoint(), prizeX, prizeY,
            powerUpChanged, mario.getMarioForm().isSuper(), mario.getMarioForm().isFire()
        );
        
        System.out.println("[PrizeSync] Prize collected by " + playerId + ": " + prizeType + 
                          " (+" + prize.getPoint() + " points, powerUp: " + powerUpChanged + ")");
        
        return message;
    }
    
    /**
     * Apply prize collection effects from network message
     */
    public void applyPrizeCollection(PrizeCollectionMessage message) {
        String prizeId = generatePrizeId(message.getPrizeType(), message.getPrizeX(), message.getPrizeY());
        
        // Check if this prize was already processed
        if (collectedPrizes.containsKey(prizeId)) {
            return;
        }
        
        // Mark as processed
        collectedPrizes.put(prizeId, System.currentTimeMillis());
        
        // Get Mario object
        Mario mario = message.getPlayerId().equals("mario") ? gameEngine.getMario() : gameEngine.getMario2();
        if (mario == null) {
            return;
        }
        
        // Apply points
        mario.acquirePoints(message.getPrizeValue());
        
        // Apply coin if it's a coin prize
        if ("Coin".equals(message.getPrizeType())) {
            mario.acquireCoin();
        }
        
        // Apply power-up changes if needed
        if (message.hasPowerUpChange()) {
            updateMarioForm(mario, message.isSuper(), message.isFire());
        }
        
        System.out.println("[PrizeSync] Applied prize collection for " + message.getPlayerId() + 
                          ": " + message.getPrizeType() + " (+" + message.getPrizeValue() + " points)");
    }
    
    /**
     * Apply prize effects locally (for server-side processing)
     */
    private void applyPrizeEffects(Mario mario, Prize prize, String prizeType) {
        // Apply points
        mario.acquirePoints(prize.getPoint());
        
        // Apply specific prize effects
        switch (prizeType) {
            case "Coin":
                mario.acquireCoin();
                gameEngine.playCoin();
                break;
                
            case "SuperMushroom":
                if (!mario.getMarioForm().isSuper()) {
                    updateMarioForm(mario, true, false);
                    gameEngine.playSuperMushroom();
                }
                break;
                
            case "FireFlower":
                if (!mario.getMarioForm().isFire()) {
                    updateMarioForm(mario, true, true);
                    gameEngine.playFireFlower();
                }
                break;
                
            case "OneUpMushroom":
                mario.setRemainingLives(mario.getRemainingLives() + 1);
                gameEngine.playOneUp();
                break;
        }
    }
    
    /**
     * Update Mario's form with proper animation and dimensions
     */
    private void updateMarioForm(Mario mario, boolean isSuper, boolean isFire) {
        try {
            ImageLoader imageLoader = new ImageLoader();
            java.awt.image.BufferedImage[] leftFrames;
            java.awt.image.BufferedImage[] rightFrames;
            
            int form = MarioForm.SMALL;
            if (isFire) {
                form = MarioForm.FIRE;
            } else if (isSuper) {
                form = MarioForm.SUPER;
            }
            
            String whichMario = mario.getWhichMario();
            if ("mario".equals(whichMario)) {
                leftFrames = imageLoader.getLeftFrames(form);
                rightFrames = imageLoader.getRightFrames(form);
            } else {
                leftFrames = imageLoader.getLeftFrames2(form);
                rightFrames = imageLoader.getRightFrames2(form);
            }
            
            Animation animation = new Animation(leftFrames, rightFrames);
            MarioForm newForm = new MarioForm(animation, isSuper, isFire, whichMario);
            mario.setMarioForm(newForm);
            
            // Set appropriate dimensions
            if (isFire || isSuper) {
                mario.setDimension(48, 96);
            } else {
                mario.setDimension(48, 48);
            }
        } catch (Exception e) {
            System.err.println("Error updating Mario form: " + e.getMessage());
        }
    }
    
    /**
     * Generate unique prize ID based on type and position
     */
    private String generatePrizeId(Prize prize, double x, double y) {
        return prize.getClass().getSimpleName() + "_" + (int)x + "_" + (int)y;
    }
    
    /**
     * Generate unique prize ID from message data
     */
    private String generatePrizeId(String prizeType, double x, double y) {
        return prizeType + "_" + (int)x + "_" + (int)y;
    }
    
    /**
     * Clean up old collected prizes to prevent memory leaks
     */
    public void cleanupOldPrizes() {
        long currentTime = System.currentTimeMillis();
        collectedPrizes.entrySet().removeIf(entry -> 
            currentTime - entry.getValue() > PRIZE_COLLECTION_TIMEOUT_MS);
    }
    
    /**
     * Check if a prize has already been collected
     */
    public boolean isPrizeCollected(Prize prize, double x, double y) {
        String prizeId = generatePrizeId(prize, x, y);
        return collectedPrizes.containsKey(prizeId);
    }
}
