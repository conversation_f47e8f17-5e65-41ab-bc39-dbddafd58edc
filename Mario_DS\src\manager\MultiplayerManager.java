package manager;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import manager.network.ConnectionManager;
import manager.network.NetworkUtils;
import manager.network.NetworkMessage.PrizeCollectionMessage;
import manager.network.PrizeSyncManager;

/**
 * Manages multiplayer functionality including local and network multiplayer
 * modes. Handles input routing, game state synchronization, and network
 * communication.
 */
public class MultiplayerManager {

  private GameEngine gameEngine;
  private GameMode currentMode;
  private AtomicInteger selectedMultiplayerOption;

  // Network components
  private ConnectionManager connectionManager;

  // Player management
  private ConcurrentHashMap<String, String> playerConnections;
  private String localPlayerId;
  private String remotePlayerId;

  // Connection state
  private boolean isConnected;
  private boolean isHost;
  private boolean isReconnecting; // Track if we're in a reconnection scenario
  private int hostPort;

  // Input buffering for network sync
  private InputBuffer inputBuffer;

  // Prize synchronization
  private PrizeSyncManager prizeSyncManager;

  public MultiplayerManager(GameEngine gameEngine) {
    this.gameEngine = gameEngine;
    this.currentMode = GameMode.LOCAL_SINGLE_PLAYER;
    this.selectedMultiplayerOption = new AtomicInteger(0);
    this.playerConnections = new ConcurrentHashMap<>();
    this.isConnected = false;
    this.isHost = false;
    this.isReconnecting = false;
    this.hostPort = 12345; // Default port
    this.inputBuffer = new InputBuffer();
    this.connectionManager = new ConnectionManager(this);

    // Initialize player IDs
    this.localPlayerId = "mario";
    this.remotePlayerId = "mario2";

    // Initialize prize sync manager (will be updated when mode changes)
    this.prizeSyncManager = new PrizeSyncManager(gameEngine, false);
  }

  /**
   * Get the current game mode
   */
  public GameMode getCurrentMode() {
    return currentMode;
  }

  /**
   * Get display name for current mode
   */
  public String getCurrentModeDisplayName() {
    switch (currentMode) {
    case LOCAL_SINGLE_PLAYER:
      return "Single Player";
    case LOCAL_MULTIPLAYER:
      return "Local Multiplayer";
    case NETWORK_HOST:
      return "Network Host" + (isConnected ? " (Connected)" : " (Waiting)");
    case NETWORK_CLIENT:
      return "Network Client" + (isConnected ? " (Connected)" : " (Disconnected)");
    default:
      return "Unknown";
    }
  }

  /**
   * Get currently selected multiplayer option for UI
   */
  public int getSelectedMultiplayerOption() {
    return selectedMultiplayerOption.get();
  }

  /**
   * Set selected multiplayer option
   */
  public void setSelectedMultiplayerOption(int option) {
    selectedMultiplayerOption.set(Math.max(0, Math.min(2, option)));
  }

  /**
   * Handle multiplayer mode selection input
   */
  public void handleMultiplayerModeSelection(ButtonAction input) {
    if (input == ButtonAction.GO_UP) {
      setSelectedMultiplayerOption(getSelectedMultiplayerOption() - 1);
    } else if (input == ButtonAction.GO_DOWN) {
      setSelectedMultiplayerOption(getSelectedMultiplayerOption() + 1);
    } else if (input == ButtonAction.SELECT) {
      selectMultiplayerMode();
    } else if (input == ButtonAction.GO_TO_START_SCREEN) {
      gameEngine.setGameStatus(GameStatus.START_SCREEN);
    }
  }

  /**
   * Select and activate the chosen multiplayer mode
   */
  private void selectMultiplayerMode() {
    int option = getSelectedMultiplayerOption();

    switch (option) {
    case 0: // Local Multiplayer
      setGameMode(GameMode.LOCAL_MULTIPLAYER);
      initializeLocalMultiplayer();
      gameEngine.setGameStatus(GameStatus.MAP_SELECTION);
      break;

    case 1: // Network Host
      startNetworkHost();
      break;

    case 2: // Network Client
      startNetworkClient();
      break;
    }
  }

  /**
   * Set the current game mode
   */
  public void setGameMode(GameMode mode) {
    this.currentMode = mode;

    // Configure player setup based on mode
    switch (mode) {
    case LOCAL_SINGLE_PLAYER:
      localPlayerId = "mario";
      remotePlayerId = null;
      break;

    case LOCAL_MULTIPLAYER:
      localPlayerId = "mario";
      remotePlayerId = "mario2";
      break;

    case NETWORK_HOST:
      isHost = true;
      localPlayerId = "mario";
      remotePlayerId = "mario2";
      // Update prize sync manager for server mode
      prizeSyncManager = new PrizeSyncManager(gameEngine, true);
      break;

    case NETWORK_CLIENT:
      isHost = false;
      localPlayerId = "mario2";
      remotePlayerId = "mario";
      // Update prize sync manager for client mode
      prizeSyncManager = new PrizeSyncManager(gameEngine, false);
      break;
    }

    // Configure game state management for the new mode
    gameEngine.configureGameStateForMultiplayer(mode);
  }

  /**
   * Initialize local multiplayer mode
   */
  private void initializeLocalMultiplayer() {
    System.out.println("Initializing local multiplayer mode");

    // Ensure both players are properly set up
    localPlayerId = "mario";
    remotePlayerId = "mario2";

    // Reset any network state
    isConnected = false;
    isHost = false;

    System.out.println("Local multiplayer initialized - Player 1: WASD+Space, Player 2: Arrow Keys+P");
  }

  /**
   * Start network host mode
   */
  private void startNetworkHost() {
    try {
      setGameMode(GameMode.NETWORK_HOST);
      if (connectionManager.startServer(hostPort)) {
        gameEngine.setGameStatus(GameStatus.WAITING_FOR_SERVER);
        String localIP = NetworkUtils.getLocalIPAddress();
        System.out.println("Started network host on port " + hostPort);
        System.out.println("Other players can connect to: " + localIP + ":" + hostPort);
      } else {
        throw new RuntimeException("Failed to start server");
      }
    } catch (Exception e) {
      System.err.println("Failed to start network host: " + e.getMessage());
      gameEngine.setGameStatus(GameStatus.MULTIPLAYER_MODE_SELECTION);
    }
  }

  /**
   * Start network client mode
   */
  private void startNetworkClient() {
    // For now, use localhost for testing
    // In a full implementation, this would show a dialog to enter IP
    String defaultHost = "localhost";
    System.out.println("Connecting to default host: " + defaultHost);
    System.out.println("Note: In a full implementation, this would show an IP entry dialog");
    startNetworkClient(defaultHost);
  }

  /**
   * Start network client with specific host address
   */
  public void startNetworkClient(String hostAddress) {
    try {
      setGameMode(GameMode.NETWORK_CLIENT);
      if (connectionManager.connectToServer(hostAddress, hostPort)) {
        gameEngine.setGameStatus(GameStatus.WAITING_FOR_SERVER);
        System.out.println("Connecting to host at " + hostAddress + ":" + hostPort);
      } else {
        throw new RuntimeException("Failed to connect to server");
      }
    } catch (Exception e) {
      System.err.println("Failed to connect to host: " + e.getMessage());
      gameEngine.setGameStatus(GameStatus.MULTIPLAYER_MODE_SELECTION);
    }
  }

  /**
   * Handle local input for both local and network multiplayer
   */
  public void handleLocalInput(ButtonAction action, boolean isPlayer1) {
    if (currentMode == GameMode.LOCAL_SINGLE_PLAYER) {
      // Single player - only handle player 1 input
      if (isPlayer1) {
        gameEngine.receiveInputMario(action);
      }
    } else if (currentMode == GameMode.LOCAL_MULTIPLAYER) {
      // Local multiplayer - handle both players
      if (isPlayer1) {
        gameEngine.receiveInputMario(action);
      } else {
        gameEngine.receiveInputMario2(action);
      }
    } else if (currentMode.isNetworkMode()) {
      // Network multiplayer - send input over network
      handleNetworkInput(action, isPlayer1);
    }
  }

  /**
   * Handle network input
   */
  private void handleNetworkInput(ButtonAction action, boolean isPlayer1) {
    // Only handle input for the local player in network mode
    if (!isPlayer1) {
      return; // In network mode, only local player input is handled
    }

    String playerId = localPlayerId;
    long timestamp = System.currentTimeMillis();

    // Buffer input for network transmission
    inputBuffer.addInput(playerId, action, timestamp);

    // Send input over network if connected
    if (isConnected && connectionManager != null) {
      connectionManager.sendPlayerInput(playerId, action, timestamp);
    } else {
      // If not connected (during reconnection), still buffer the input for when
      // connection is restored
      System.out.println("Buffering input during disconnection: " + action + " for player " + playerId);
    }

    // Apply input locally for prediction/immediate response
    // This should work even during reconnection states now
    if (localPlayerId.equals("mario")) {
      gameEngine.receiveInputMario(action);
    } else {
      gameEngine.receiveInputMario2(action);
    }
  }

  /**
   * Called when a network connection is established
   */
  public void onConnectionEstablished() {
    isConnected = true;
    String role = isHost ? "[Server]" : "[Client]";
    System.out.println("\u001B[35m" + role + "\u001B[0m Network connection established");

    // Check if this is a reconnection scenario using our reliable flag
    if (isReconnecting) {
      System.out.println("\u001B[35m" + role + "\u001B[0m Reconnection detected - waiting for server state sync");
      gameEngine.setGameStatus(GameStatus.WAITING_FOR_SERVER);

      // Flush any buffered input that was accumulated during disconnection
      flushBufferedInput();

      // Note: isReconnecting flag will be reset when game transitions to
      // RUNNING state
    } else {
      // Initial connection - go to map selection
      System.out.println("\u001B[35m" + role + "\u001B[0m Initial connection - switching to MAP_SELECTION");
      gameEngine.setGameStatus(GameStatus.MAP_SELECTION);
    }
  }

  /**
   * Called when network connection is lost
   */
  public void onConnectionLost() {
    isConnected = false;
    isReconnecting = true; // Mark that we're now in a reconnection scenario
    String role = isHost ? "[Server]" : "[Client]";
    System.out.println("\u001B[35m" + role + "\u001B[0m Network connection lost, switching to RECONNECTING");
    gameEngine.setGameStatus(GameStatus.RECONNECTING);
  }

  /**
   * Called when the game transitions to RUNNING state to reset reconnection
   * flag
   */
  public void onGameRunning() {
    if (isReconnecting) {
      isReconnecting = false;
      String role = isHost ? "[Server]" : "[Client]";
      System.out.println("\u001B[35m" + role + "\u001B[0m Game is now running - reconnection complete");
    }
  }

  /**
   * Flush buffered input to the server after reconnection
   */
  private void flushBufferedInput() {
    if (inputBuffer != null && connectionManager != null && isConnected) {
      String role = isHost ? "[Server]" : "[Client]";
      System.out.println("\u001B[35m" + role + "\u001B[0m Flushing buffered input after reconnection");

      // Get unprocessed inputs for the local player and send them
      var unprocessedInputs = inputBuffer.getUnprocessedInputs(localPlayerId);
      for (var inputEvent : unprocessedInputs) {
        if (inputEvent != null) {
          connectionManager.sendPlayerInput(localPlayerId, inputEvent.getAction(), inputEvent.getTimestamp());
          System.out.println("\u001B[35m" + role + "\u001B[0m Flushed input: " + inputEvent.getAction());
        }
      }

      // Mark all inputs as processed after flushing
      inputBuffer.markProcessed(localPlayerId, System.currentTimeMillis());

      System.out.println("\u001B[35m" + role + "\u001B[0m Flushed " + unprocessedInputs.size() + " buffered inputs");
    }
  }

  /**
   * Handle incoming network input from remote player
   */
  public void handleRemotePlayerInput(String playerId, ButtonAction action, long timestamp) {
    String role = isHost ? "[Server]" : "[Client]";
    System.out.println("\u001B[35m" + role + "\u001B[0m Handling remote input - Player: " + playerId + ", Action: "
        + action + ", Local: " + localPlayerId);

    // Apply remote player input to the appropriate Mario character
    if (playerId.equals("mario") && !localPlayerId.equals("mario")) {
      System.out.println("\u001B[35m" + role + "\u001B[0m Applying input to Mario 1");
      gameEngine.receiveInputMario(action);
    } else if (playerId.equals("mario2") && !localPlayerId.equals("mario2")) {
      System.out.println("\u001B[35m" + role + "\u001B[0m Applying input to Mario 2");
      gameEngine.receiveInputMario2(action);
    } else {
      System.out.println("\u001B[35m" + role + "\u001B[0m Ignoring input (local player or invalid)");
    }

    // Record the input in the buffer for synchronization
    inputBuffer.addProcessedInput(playerId, action, timestamp);
  }

  /**
   * Handle prize collection with network synchronization
   */
  public boolean handlePrizeCollection(String playerId, model.prize.Prize prize, double prizeX, double prizeY) {
    if (currentMode.isNetworkMode() && isHost) {
      // Server handles authoritative prize collection
      PrizeCollectionMessage message = prizeSyncManager.handlePrizeCollection(playerId, prize, prizeX, prizeY);
      if (message != null) {
        // Send to client
        connectionManager.sendPrizeCollection(message);
        return true;
      }
      return false; // Prize already collected
    } else if (currentMode.isNetworkMode() && !isHost) {
      // Client should not handle prize collection directly
      // This will be handled by server and synchronized back
      return false;
    } else {
      // Local mode - handle directly
      return prizeSyncManager.handlePrizeCollection(playerId, prize, prizeX, prizeY) != null;
    }
  }

  /**
   * Apply prize collection from network message
   */
  public void applyPrizeCollection(PrizeCollectionMessage message) {
    if (prizeSyncManager != null) {
      prizeSyncManager.applyPrizeCollection(message);
    }
  }

  /**
   * Check if a prize has already been collected
   */
  public boolean isPrizeCollected(model.prize.Prize prize, double x, double y) {
    return prizeSyncManager != null && prizeSyncManager.isPrizeCollected(prize, x, y);
  }

  /**
   * Get the game engine reference
   */
  public GameEngine getGameEngine() {
    return gameEngine;
  }

  /**
   * Check if currently connected to network
   */
  public boolean isConnected() {
    return isConnected;
  }

  /**
   * Check if this instance is the host
   */
  public boolean isHost() {
    return isHost;
  }

  /**
   * Get control instructions for current multiplayer mode
   */
  public String getControlInstructions() {
    switch (currentMode) {
    case LOCAL_SINGLE_PLAYER:
      return "Player 1: WASD to move, Space to fire";

    case LOCAL_MULTIPLAYER:
      return "Player 1: WASD + Space | Player 2: Arrow Keys + P";

    case NETWORK_HOST:
      return "Host: WASD + Space (Waiting for client...)";

    case NETWORK_CLIENT:
      return "Client: Arrow Keys + P (Connected to host)";

    default:
      return "Unknown mode";
    }
  }

  /**
   * Get detailed multiplayer status for debugging
   */
  public String getMultiplayerStatus() {
    StringBuilder status = new StringBuilder();
    status.append("Mode: ").append(getCurrentModeDisplayName()).append("\n");
    status.append("Local Player: ").append(localPlayerId).append("\n");

    if (remotePlayerId != null) {
      status.append("Remote Player: ").append(remotePlayerId).append("\n");
    }

    if (currentMode.isNetworkMode()) {
      status.append("Connected: ").append(isConnected).append("\n");
      status.append("Host: ").append(isHost).append("\n");

      if (connectionManager != null) {
        status.append("Connection: ").append(connectionManager.getConnectionInfo()).append("\n");
      }
    }

    return status.toString();
  }

  /**
   * Get host information for display (when hosting)
   */
  public String getHostInfo() {
    if (currentMode == GameMode.NETWORK_HOST && isHost) {
      String localIP = NetworkUtils.getLocalIPAddress();
      return "Host IP: " + localIP + ":" + hostPort + "\nShare this with other players";
    }
    return "";
  }

  /**
   * Get the local player ID
   */
  public String getLocalPlayerId() {
    return localPlayerId;
  }

  /**
   * Set the local player ID (used during network reconnection)
   */
  public void setLocalPlayerId(String playerId) {
    String role = isHost ? "[Server]" : "[Client]";
    System.out.println("\u001B[35m" + role + "\u001B[0m Setting local player ID to: " + playerId);
    this.localPlayerId = playerId;
  }

  /**
   * Get the remote player ID
   */
  public String getRemotePlayerId() {
    return remotePlayerId;
  }

  /**
   * Cleanup network resources
   */
  public void cleanup() {
    if (connectionManager != null) {
      connectionManager.cleanup();
    }

    isConnected = false;
    playerConnections.clear();
  }
}
