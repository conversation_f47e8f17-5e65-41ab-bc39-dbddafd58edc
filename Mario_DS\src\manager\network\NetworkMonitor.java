package manager.network;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Monitors network performance metrics including latency, packet loss,
 * and connection quality for debugging and optimization purposes.
 */
public class NetworkMonitor {
    
    // Latency tracking
    private final ConcurrentLinkedQueue<Long> latencyHistory;
    private final AtomicLong totalLatency;
    private final AtomicInteger latencyCount;
    private final AtomicLong minLatency;
    private final AtomicLong maxLatency;
    
    // Packet tracking
    private final AtomicInteger packetsSent;
    private final AtomicInteger packetsReceived;
    private final AtomicInteger packetsLost;
    
    // Connection quality
    private final AtomicLong lastPacketTime;
    private final AtomicInteger connectionDrops;
    private final AtomicLong connectionStartTime;
    
    // Configuration
    private static final int MAX_LATENCY_HISTORY = 100;
    private static final long PACKET_TIMEOUT_MS = 5000; // 5 seconds
    private static final long CONNECTION_TIMEOUT_MS = 10000; // 10 seconds
    
    public NetworkMonitor() {
        this.latencyHistory = new ConcurrentLinkedQueue<>();
        this.totalLatency = new AtomicLong(0);
        this.latencyCount = new AtomicInteger(0);
        this.minLatency = new AtomicLong(Long.MAX_VALUE);
        this.maxLatency = new AtomicLong(0);
        
        this.packetsSent = new AtomicInteger(0);
        this.packetsReceived = new AtomicInteger(0);
        this.packetsLost = new AtomicInteger(0);
        
        this.lastPacketTime = new AtomicLong(System.currentTimeMillis());
        this.connectionDrops = new AtomicInteger(0);
        this.connectionStartTime = new AtomicLong(System.currentTimeMillis());
    }
    
    /**
     * Record a latency measurement
     */
    public void recordLatency(long latencyMs) {
        // Update running statistics
        totalLatency.addAndGet(latencyMs);
        latencyCount.incrementAndGet();
        
        // Update min/max
        minLatency.updateAndGet(current -> Math.min(current, latencyMs));
        maxLatency.updateAndGet(current -> Math.max(current, latencyMs));
        
        // Add to history (with size limit)
        latencyHistory.offer(latencyMs);
        while (latencyHistory.size() > MAX_LATENCY_HISTORY) {
            latencyHistory.poll();
        }
        
        // Update last packet time
        lastPacketTime.set(System.currentTimeMillis());
    }
    
    /**
     * Record a packet sent
     */
    public void recordPacketSent() {
        packetsSent.incrementAndGet();
    }
    
    /**
     * Record a packet received
     */
    public void recordPacketReceived() {
        packetsReceived.incrementAndGet();
        lastPacketTime.set(System.currentTimeMillis());
    }
    
    /**
     * Record a packet loss
     */
    public void recordPacketLoss() {
        packetsLost.incrementAndGet();
    }
    
    /**
     * Record a connection drop
     */
    public void recordConnectionDrop() {
        connectionDrops.incrementAndGet();
    }
    
    /**
     * Reset connection start time (for reconnections)
     */
    public void resetConnectionTime() {
        connectionStartTime.set(System.currentTimeMillis());
    }
    
    /**
     * Get average latency in milliseconds
     */
    public double getAverageLatency() {
        int count = latencyCount.get();
        if (count == 0) return 0.0;
        return (double) totalLatency.get() / count;
    }
    
    /**
     * Get minimum latency in milliseconds
     */
    public long getMinLatency() {
        long min = minLatency.get();
        return min == Long.MAX_VALUE ? 0 : min;
    }
    
    /**
     * Get maximum latency in milliseconds
     */
    public long getMaxLatency() {
        return maxLatency.get();
    }
    
    /**
     * Get recent latency (last 10 measurements average)
     */
    public double getRecentLatency() {
        if (latencyHistory.isEmpty()) return 0.0;
        
        long sum = 0;
        int count = 0;
        Long[] history = latencyHistory.toArray(new Long[0]);
        
        // Take last 10 measurements
        int start = Math.max(0, history.length - 10);
        for (int i = start; i < history.length; i++) {
            sum += history[i];
            count++;
        }
        
        return count > 0 ? (double) sum / count : 0.0;
    }
    
    /**
     * Get packet loss percentage
     */
    public double getPacketLossPercentage() {
        int sent = packetsSent.get();
        if (sent == 0) return 0.0;
        
        int lost = packetsLost.get();
        return (double) lost / sent * 100.0;
    }
    
    /**
     * Check if connection is stable
     */
    public boolean isConnectionStable() {
        long timeSinceLastPacket = System.currentTimeMillis() - lastPacketTime.get();
        return timeSinceLastPacket < CONNECTION_TIMEOUT_MS;
    }
    
    /**
     * Get connection quality rating (0-100)
     */
    public int getConnectionQuality() {
        double avgLatency = getAverageLatency();
        double packetLoss = getPacketLossPercentage();
        boolean stable = isConnectionStable();
        
        int quality = 100;
        
        // Deduct points for high latency
        if (avgLatency > 200) quality -= 40;
        else if (avgLatency > 100) quality -= 20;
        else if (avgLatency > 50) quality -= 10;
        
        // Deduct points for packet loss
        if (packetLoss > 10) quality -= 30;
        else if (packetLoss > 5) quality -= 15;
        else if (packetLoss > 1) quality -= 5;
        
        // Deduct points for instability
        if (!stable) quality -= 25;
        
        return Math.max(0, quality);
    }
    
    /**
     * Get connection uptime in milliseconds
     */
    public long getConnectionUptime() {
        return System.currentTimeMillis() - connectionStartTime.get();
    }
    
    /**
     * Get total packets sent
     */
    public int getPacketsSent() {
        return packetsSent.get();
    }
    
    /**
     * Get total packets received
     */
    public int getPacketsReceived() {
        return packetsReceived.get();
    }
    
    /**
     * Get total packets lost
     */
    public int getPacketsLost() {
        return packetsLost.get();
    }
    
    /**
     * Get total connection drops
     */
    public int getConnectionDrops() {
        return connectionDrops.get();
    }
    
    /**
     * Get comprehensive network statistics as a formatted string
     */
    public String getNetworkStats() {
        return String.format(
            "Network Stats:\n" +
            "  Latency: %.1fms (min: %dms, max: %dms, recent: %.1fms)\n" +
            "  Packets: %d sent, %d received, %d lost (%.1f%% loss)\n" +
            "  Quality: %d/100, Stable: %s\n" +
            "  Uptime: %.1fs, Drops: %d",
            getAverageLatency(), getMinLatency(), getMaxLatency(), getRecentLatency(),
            getPacketsSent(), getPacketsReceived(), getPacketsLost(), getPacketLossPercentage(),
            getConnectionQuality(), isConnectionStable(),
            getConnectionUptime() / 1000.0, getConnectionDrops()
        );
    }
    
    /**
     * Reset all statistics
     */
    public void reset() {
        latencyHistory.clear();
        totalLatency.set(0);
        latencyCount.set(0);
        minLatency.set(Long.MAX_VALUE);
        maxLatency.set(0);
        
        packetsSent.set(0);
        packetsReceived.set(0);
        packetsLost.set(0);
        
        connectionDrops.set(0);
        connectionStartTime.set(System.currentTimeMillis());
        lastPacketTime.set(System.currentTimeMillis());
    }
}
