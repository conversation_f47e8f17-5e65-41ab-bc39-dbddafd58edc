package manager.network;

import java.io.*;
import java.net.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

import manager.ButtonAction;
import manager.GameStatus;
import manager.MultiplayerManager;
import manager.network.NetworkMessage.*;
import manager.network.NetworkMessage.StatusSyncMessage;
import model.enemy.Enemy;

/**
 * Network client for connecting to multiplayer game hosts. Handles server
 * communication, input transmission, and game state synchronization.
 */
public class NetworkClient {

  private static final long RECONNECT_INTERVAL_MS = 5000; // 5 second reconnect
                                                          // attempts
  private static final long CONNECTION_TIMEOUT_MS = 10000; // 10 second
                                                           // connection timeout

  private final MultiplayerManager multiplayerManager;
  private final String serverAddress;
  private final int serverPort;

  private Socket socket;
  private ObjectInputStream input;
  private ObjectOutputStream output;
  private ExecutorService threadPool;
  private ScheduledExecutorService scheduler;
  private AtomicBoolean connected;
  private AtomicBoolean connecting;

  private String assignedPlayerId;
  private long lastPingTime;

  // Network monitoring
  private NetworkMonitor networkMonitor;

  public NetworkClient(MultiplayerManager multiplayerManager, String serverAddress, int serverPort) {
    this.multiplayerManager = multiplayerManager;
    this.serverAddress = serverAddress;
    this.serverPort = serverPort;

    this.connected = new AtomicBoolean(false);
    this.connecting = new AtomicBoolean(false);
    this.threadPool = Executors.newCachedThreadPool();
    this.scheduler = Executors.newScheduledThreadPool(1);
    this.lastPingTime = 0;

    // Initialize network monitoring
    this.networkMonitor = new NetworkMonitor();
  }

  /**
   * Connect to the server
   */
  public void connect() {
    if (connected.get() || connecting.get()) {
      return;
    }

    connecting.set(true);
    threadPool.submit(this::doConnect);
  }

  /**
   * Disconnect from the server
   */
  public void disconnect() {
    if (!connected.get() && !connecting.get()) {
      return;
    }

    connected.set(false);
    connecting.set(false);

    try {
      if (output != null) {
        DisconnectMessage disconnectMsg = new DisconnectMessage("Client disconnecting");
        output.writeObject(disconnectMsg);
        output.flush();
      }
    } catch (IOException e) {
      // Ignore errors when disconnecting
    }

    try {
      if (socket != null && !socket.isClosed()) {
        socket.close();
      }
    } catch (IOException e) {
      System.err.println("Error closing socket: " + e.getMessage());
    }

    threadPool.shutdown();
    scheduler.shutdown();

    try {
      if (!threadPool.awaitTermination(5, TimeUnit.SECONDS)) {
        threadPool.shutdownNow();
      }
      if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
        scheduler.shutdownNow();
      }
    } catch (InterruptedException e) {
      threadPool.shutdownNow();
      scheduler.shutdownNow();
    }

    multiplayerManager.onConnectionLost();
    System.out.println("Disconnected from server");
  }

  /**
   * Send player input to the server
   */
  public void sendPlayerInput(String playerId, ButtonAction action, long timestamp) {
    if (connected.get() && output != null) {
      try {
        System.out.println(
            "\u001B[34m[Client]\u001B[0m Sending input to server - Player: " + playerId + ", Action: " + action);
        PlayerInputMessage message = new PlayerInputMessage(playerId, action, true);
        output.writeObject(message);
        output.flush();
      } catch (IOException e) {
        System.err.println("\u001B[31m[Client]\u001B[0m Error sending player input: " + e.getMessage());
        handleConnectionError();
      }
    } else {
      System.out.println("\u001B[34m[Client]\u001B[0m Cannot send input - not connected or output stream null");
    }
  }

  /**
   * Check if connected to server
   */
  public boolean isConnected() {
    return connected.get();
  }

  /**
   * Get the assigned player ID from the server
   */
  public String getAssignedPlayerId() {
    return assignedPlayerId;
  }

  /**
   * Get network monitor for detailed statistics
   */
  public NetworkMonitor getNetworkMonitor() {
    return networkMonitor;
  }

  /**
   * Get network statistics as formatted string
   */
  public String getNetworkStats() {
    return networkMonitor.getNetworkStats();
  }

  /**
   * Get connection quality (0-100)
   */
  public int getConnectionQuality() {
    return networkMonitor.getConnectionQuality();
  }

  /**
   * Perform the actual connection to the server
   */
  private void doConnect() {
    try {
      System.out.println("\u001B[34m[Client]\u001B[0m Connecting to server at " + serverAddress + ":" + serverPort);

      socket = new Socket();
      socket.connect(new InetSocketAddress(serverAddress, serverPort), (int) CONNECTION_TIMEOUT_MS);

      output = new ObjectOutputStream(socket.getOutputStream());
      input = new ObjectInputStream(socket.getInputStream());

      // Send connection request
      ConnectionRequestMessage request = new ConnectionRequestMessage("Player", "1.0");
      output.writeObject(request);
      output.flush();

      // Wait for connection response
      NetworkMessage response = (NetworkMessage) input.readObject();
      if (response instanceof ConnectionResponseMessage) {
        ConnectionResponseMessage connResponse = (ConnectionResponseMessage) response;

        if (connResponse.isAccepted()) {
          assignedPlayerId = connResponse.getAssignedPlayerId();
          connected.set(true);
          connecting.set(false);

          System.out.println("\u001B[34m[Client]\u001B[0m Connected to server as player: " + assignedPlayerId);

          // Ensure multiplayer manager knows the correct player ID
          multiplayerManager.setLocalPlayerId(assignedPlayerId);

          // Note: onConnectionEstablished() is called by ConnectionManager, not
          // here
          // to avoid double calls during reconnection scenarios

          // Start message handling
          threadPool.submit(this::handleMessages);

        } else {
          System.err.println("\u001B[31m[Client]\u001B[0m Connection rejected: " + connResponse.getReason());
          connecting.set(false);
          socket.close();
        }
      } else {
        System.err.println("\u001B[31m[Client]\u001B[0m Unexpected response from server");
        connecting.set(false);
        socket.close();
      }

    } catch (IOException | ClassNotFoundException e) {
      System.err.println("\u001B[31m[Client]\u001B[0m Connection failed: " + e.getMessage());
      connecting.set(false);
      handleConnectionError();
    }
  }

  /**
   * Handle incoming messages from the server
   */
  private void handleMessages() {
    while (connected.get() && !socket.isClosed()) {
      try {
        NetworkMessage message = (NetworkMessage) input.readObject();
        handleMessage(message);

      } catch (IOException | ClassNotFoundException e) {
        if (connected.get()) {
          System.err.println("Error receiving message: " + e.getMessage());
          handleConnectionError();
        }
        break;
      }
    }
  }

  /**
   * Handle a specific message from the server
   */
  private void handleMessage(NetworkMessage message) {
    switch (message.getType()) {
    case GAME_STATE:
      handleGameStateUpdate((GameStateMessage) message);
      break;

    case PLAYER_INPUT:
      handlePlayerInput((PlayerInputMessage) message);
      break;

    case PING:
      handlePing((PingMessage) message);
      break;

    case STATUS_SYNC:
      handleStatusSync((StatusSyncMessage) message);
      break;

    case DISCONNECT:
      System.out.println("Server disconnected: " + ((DisconnectMessage) message).getReason());
      handleConnectionError();
      break;

    default:
      System.out.println("Unhandled message type: " + message.getType());
      break;
    }
  }

  /**
   * Handle game state update from server
   */
  private void handleGameStateUpdate(GameStateMessage message) {
    try {
      var gameEngine = multiplayerManager.getGameEngine();
      var gameStatus = gameEngine.getGameStatus();

      var mario1 = gameEngine.getMario();
      var mario2 = gameEngine.getMario2();

      System.out.println("\u001B[34m[Client]\u001B[0m Received game state - Status: " + gameStatus + ", Mario1: "
          + (mario1 != null ? "OK" : "NULL") + ", Mario2: " + (mario2 != null ? "OK" : "NULL") + ", Server running: "
          + message.isGameRunning());

      // Check if server is running but client hasn't loaded the map yet
      if (message.isGameRunning() && (mario1 == null || mario2 == null)) {
        System.out
            .println("\u001B[34m[Client]\u001B[0m Server is running but client map not loaded - triggering map load");

        // If client is still waiting for server, transition to map selection to
        // load the map
        if (gameStatus == GameStatus.WAITING_FOR_SERVER || gameStatus == GameStatus.RECONNECTING) {
          System.out.println("\u001B[34m[Client]\u001B[0m Transitioning from " + gameStatus + " to MAP_SELECTION");
          gameEngine.setGameStatus(GameStatus.MAP_SELECTION);
        }

        // Try to load the map if we're in map selection and server is running
        if (gameStatus == GameStatus.MAP_SELECTION || gameStatus == GameStatus.WAITING_FOR_SERVER) {
          System.out.println("\u001B[34m[Client]\u001B[0m Attempting to load map to sync with running server");
          gameEngine.selectMapViaKeyboard();

          // After attempting to load, get updated Mario objects
          mario1 = gameEngine.getMario();
          mario2 = gameEngine.getMario2();
        }
      }

      // If server is running and we have Mario objects loaded, transition to
      // RUNNING
      if (message.isGameRunning() && mario1 != null && mario2 != null && (gameStatus == GameStatus.WAITING_FOR_SERVER
          || gameStatus == GameStatus.RECONNECTING || gameStatus == GameStatus.MAP_SELECTION)) {
        System.out
            .println("\u001B[34m[Client]\u001B[0m Server is running and map is loaded - transitioning to RUNNING");
        gameEngine.setGameStatus(GameStatus.RUNNING);
      }

      // Apply state updates if both Mario objects are available (map is loaded)
      if (mario1 != null && mario2 != null) {
        System.out.println("\u001B[34m[Client]\u001B[0m Applying game state update");
        // Update Mario 1 state (host player)
        PlayerState mario1State = message.getMario1State();
        if (mario1State != null) {
          NetworkUtils.applyPlayerStateToMario(mario1State, mario1);
        }

        // Update Mario 2 state (client player) - but only if not local input
        PlayerState mario2State = message.getMario2State();
        if (mario2State != null && !assignedPlayerId.equals("mario2")) {
          NetworkUtils.applyPlayerStateToMario(mario2State, mario2);
        }

        // Synchronize enemy states
        try {
          var mapManager = gameEngine.getMapManager();
          if (mapManager != null && mapManager.getMap() != null) {
            var enemyStates = message.getEnemyStates();
            if (enemyStates != null && !enemyStates.isEmpty()) {
              var enemies = mapManager.getMap().getEnemies();
              if (enemies != null && !enemies.isEmpty()) {
                System.out.println("\u001B[34m[Client]\u001B[0m Synchronizing enemies - Server: " + enemyStates.size()
                    + ", Local: " + enemies.size());

                // Sync enemies by unique ID rather than array index
                for (var enemyState : enemyStates) {
                  if (enemyState != null) {
                    int enemyUniqueId = enemyState.getEnemyId();

                    // Find enemy by unique ID (robust against viewport culling
                    // and enemy count mismatches)
                    Enemy matchingEnemy = null;
                    for (var enemy : enemies) {
                      if (enemy != null && enemy.getUniqueId() == enemyUniqueId) {
                        matchingEnemy = enemy;
                        break;
                      }
                    }

                    if (matchingEnemy != null) {
                      // Check type compatibility (allow some flexibility)
                      String localType = matchingEnemy.getClass().getSimpleName();
                      String serverType = enemyState.getEnemyType();

                      if (localType.equals(serverType) || isCompatibleEnemyType(localType, serverType)) {
                        // Sync enemy state
                        matchingEnemy.setX(enemyState.getX());
                        matchingEnemy.setY(enemyState.getY());
                        matchingEnemy.setVelX(enemyState.getVelX());
                        matchingEnemy.setVelY(enemyState.getVelY());
                        matchingEnemy.setFalling(enemyState.isFalling());
                        matchingEnemy.setJumping(enemyState.isJumping());

                        System.out.println("\u001B[34m[Client]\u001B[0m Synced enemy " + enemyUniqueId + " ("
                            + serverType + ") at (" + enemyState.getX() + "," + enemyState.getY() + ")");
                      } else {
                        System.out.println("\u001B[34m[Client]\u001B[0m Type mismatch for enemy " + enemyUniqueId
                            + ": local=" + localType + ", server=" + serverType);
                      }
                    } else {
                      System.out.println("\u001B[34m[Client]\u001B[0m Enemy with unique ID " + enemyUniqueId
                          + " not found locally (total enemies: " + enemies.size() + ")");
                    }
                  }
                }
              } else {
                System.out.println("\u001B[34m[Client]\u001B[0m No local enemies to synchronize");
              }
            } else {
              System.out.println("\u001B[34m[Client]\u001B[0m No enemy states received from server");
            }
          } else {
            System.out.println("\u001B[34m[Client]\u001B[0m Map or MapManager not available for enemy sync");
          }
        } catch (Exception e) {
          System.err.println("\u001B[34m[Client]\u001B[0m Error synchronizing enemies: " + e.getMessage());
          e.printStackTrace();
        }
      } else {
        System.out.println("\u001B[34m[Client]\u001B[0m Skipping state update - Mario objects not ready");

        // Even if Mario objects aren't ready, we might still want to sync
        // enemies during reconnection
        var currentGameStatus = gameEngine.getGameStatus();
        if (currentGameStatus == GameStatus.WAITING_FOR_SERVER || currentGameStatus == GameStatus.RECONNECTING) {
          System.out.println(
              "\u001B[34m[Client]\u001B[0m Attempting enemy sync during reconnection state: " + currentGameStatus);
          syncEnemiesOnly(message);
        }
      }
    } catch (Exception e) {
      System.err.println("\u001B[31m[Client]\u001B[0m Error handling game state update: " + e.getMessage());
      e.printStackTrace();
    }
  }

  /**
   * Handle player input from server (other player's input)
   */
  private void handlePlayerInput(PlayerInputMessage message) {
    System.out.println("\u001B[34m[Client]\u001B[0m Received input from server - Player: " + message.getPlayerId()
        + ", Action: " + message.getAction() + ", Assigned: " + assignedPlayerId);
    if (!message.getPlayerId().equals(assignedPlayerId)) {
      // Forward remote player input to multiplayer manager
      multiplayerManager.handleRemotePlayerInput(message.getPlayerId(), message.getAction(), message.getTimestamp());
    } else {
      System.out.println("\u001B[34m[Client]\u001B[0m Ignoring own input echo");
    }
  }

  /**
   * Handle ping from server
   */
  private void handlePing(PingMessage message) {
    try {
      PongMessage pong = new PongMessage(message.getPingId());
      output.writeObject(pong);
      output.flush();
      networkMonitor.recordPacketSent();
      networkMonitor.recordPacketReceived(); // For the ping we received
    } catch (IOException e) {
      System.err.println("Error sending pong: " + e.getMessage());
    }
  }

  /**
   * Handle status synchronization from server
   */
  private void handleStatusSync(StatusSyncMessage message) {
    System.out.println("\u001B[34m[Client]\u001B[0m Received status sync - Status: " + message.getGameStatus()
        + ", Map: " + message.getSelectedMap());

    var gameEngine = multiplayerManager.getGameEngine();
    var currentStatus = gameEngine.getGameStatus();

    // Synchronize game status
    try {
      GameStatus serverStatus = GameStatus.valueOf(message.getGameStatus());
      System.out.println("\u001B[34m[Client]\u001B[0m Syncing status from " + currentStatus + " to " + serverStatus);

      // Special handling for RECONNECTING state - transition properly to sync
      // with server
      if (currentStatus == GameStatus.RECONNECTING) {
        System.out
            .println("\u001B[34m[Client]\u001B[0m Client is in RECONNECTING state, transitioning to sync with server");

        if (serverStatus == GameStatus.RUNNING) {
          // Server is running, we need to load the map and catch up
          System.out
              .println("\u001B[34m[Client]\u001B[0m Server is running - transitioning through states to catch up");

          // Set map selection first
          int serverMap = message.getSelectedMap();
          if (serverMap != gameEngine.getSelectedMap()) {
            System.out.println("\u001B[34m[Client]\u001B[0m Syncing map selection to " + serverMap);
            gameEngine.setSelectedMap(serverMap);
          }

          // Transition to WAITING_FOR_SERVER first, then load map
          gameEngine.setGameStatus(GameStatus.WAITING_FOR_SERVER);

          // Load the map to catch up with server
          gameEngine.selectMapViaKeyboard();

          // The game status will be set to RUNNING by the game state sync
          return;
        } else {
          // Server is not running, just sync to server status
          gameEngine.setGameStatus(serverStatus);
          return;
        }
      }

      // If server is running a specific map, load it on client
      if (serverStatus == GameStatus.RUNNING || serverStatus == GameStatus.MAP_SELECTION) {
        int serverMap = message.getSelectedMap();
        System.out.println(
            "\u001B[34m[Client]\u001B[0m Server map: " + serverMap + ", Client map: " + gameEngine.getSelectedMap());

        if (serverMap != gameEngine.getSelectedMap()) {
          System.out.println("\u001B[34m[Client]\u001B[0m Syncing map selection to " + serverMap);
          gameEngine.setSelectedMap(serverMap);
        }

        // If server is running and client is still waiting or in map selection,
        // load the map
        if (serverStatus == GameStatus.RUNNING
            && (currentStatus == GameStatus.WAITING_FOR_SERVER || currentStatus == GameStatus.MAP_SELECTION)) {
          System.out.println("\u001B[34m[Client]\u001B[0m Server is running - loading map and starting game");

          // First transition to MAP_SELECTION if we're still waiting
          if (currentStatus == GameStatus.WAITING_FOR_SERVER) {
            gameEngine.setGameStatus(GameStatus.MAP_SELECTION);
          }

          // Load the map
          gameEngine.selectMapViaKeyboard();
        } else {
          // Update status after map handling
          gameEngine.setGameStatus(serverStatus);
        }
      } else {
        // For other statuses, just sync directly
        gameEngine.setGameStatus(serverStatus);
      }
    } catch (IllegalArgumentException e) {
      System.err.println("\u001B[31m[Client]\u001B[0m Invalid game status from server: " + message.getGameStatus());
    }
  }

  /**
   * Synchronize only enemy states (used during reconnection when Mario objects
   * might not be ready)
   */
  private void syncEnemiesOnly(GameStateMessage message) {
    try {
      var gameEngine = multiplayerManager.getGameEngine();
      var mapManager = gameEngine.getMapManager();
      if (mapManager != null && mapManager.getMap() != null) {
        var enemyStates = message.getEnemyStates();
        if (enemyStates != null && !enemyStates.isEmpty()) {
          var enemies = mapManager.getMap().getEnemies();
          if (enemies != null && !enemies.isEmpty()) {
            System.out.println("\u001B[34m[Client]\u001B[0m Syncing enemies during reconnection - Server: "
                + enemyStates.size() + ", Local: " + enemies.size());

            // Use the same enemy sync logic as in handleGameStateUpdate
            for (var enemyState : enemyStates) {
              if (enemyState != null) {
                int enemyUniqueId = enemyState.getEnemyId();

                // Find enemy by unique ID (robust against viewport culling and
                // enemy count mismatches)
                Enemy matchingEnemy = null;
                for (var enemy : enemies) {
                  if (enemy != null && enemy.getUniqueId() == enemyUniqueId) {
                    matchingEnemy = enemy;
                    break;
                  }
                }

                if (matchingEnemy != null) {
                  String localType = matchingEnemy.getClass().getSimpleName();
                  String serverType = enemyState.getEnemyType();

                  if (localType.equals(serverType) || isCompatibleEnemyType(localType, serverType)) {
                    matchingEnemy.setX(enemyState.getX());
                    matchingEnemy.setY(enemyState.getY());
                    matchingEnemy.setVelX(enemyState.getVelX());
                    matchingEnemy.setVelY(enemyState.getVelY());
                    matchingEnemy.setFalling(enemyState.isFalling());
                    matchingEnemy.setJumping(enemyState.isJumping());

                    System.out.println("\u001B[34m[Client]\u001B[0m Reconnection enemy sync: " + enemyUniqueId + " ("
                        + serverType + ")");
                  }
                } else {
                  System.out.println("\u001B[34m[Client]\u001B[0m Reconnection: Enemy with unique ID " + enemyUniqueId
                      + " not found locally");
                }
              }
            }
          }
        }
      }
    } catch (Exception e) {
      System.err.println("\u001B[34m[Client]\u001B[0m Error in reconnection enemy sync: " + e.getMessage());
    }
  }

  /**
   * Handle connection errors and attempt reconnection
   */
  private void handleConnectionError() {
    if (connected.getAndSet(false)) {
      multiplayerManager.onConnectionLost();

      // Schedule reconnection attempt
      scheduler.schedule(() -> {
        if (!connected.get() && multiplayerManager.getCurrentMode().isNetworkMode()) {
          System.out.println("Attempting to reconnect...");
          connect();
        }
      }, RECONNECT_INTERVAL_MS, TimeUnit.MILLISECONDS);
    }
  }

  /**
   * Check if two enemy types are compatible for synchronization This allows for
   * some flexibility in enemy type matching
   */
  private boolean isCompatibleEnemyType(String localType, String serverType) {
    if (localType == null || serverType == null) {
      return false;
    }

    // Allow exact matches
    if (localType.equals(serverType)) {
      return true;
    }

    // Allow some common variations (case insensitive)
    String localLower = localType.toLowerCase();
    String serverLower = serverType.toLowerCase();

    // Handle common enemy type variations
    if ((localLower.contains("goomba") && serverLower.contains("goomba"))
        || (localLower.contains("koopa") && serverLower.contains("koopa"))
        || (localLower.contains("troopa") && serverLower.contains("troopa"))) {
      return true;
    }

    return false;
  }
}
